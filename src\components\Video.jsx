import React, { useState } from 'react';
import './Video.css';

const Video = () => {
  const [playingVideo, setPlayingVideo] = useState(null);

  const videoData = [
    {
      id: 1,
      title: "Journey to Mars: The Next Frontier of Human Exploration",
      description: "Explore the latest developments in Mars exploration technology and the ambitious plans for human colonization of the Red Planet.",
      thumbnail: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=600&h=400&fit=crop&crop=center",
      duration: "12:45",
      views: "2.3M",
      publishedAt: "3 days ago",
      publisher: {
        name: "Space Exploration Hub",
        avatar: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=40&h=40&fit=crop&crop=center"
      },
      videoUrl: "https://www.youtube.com/embed/watch?v=eawxyZRumG4",
      category: "Exploration",
      featured: true
    },
    {
      id: 2,
      title: "Black Holes: Mysteries of the Universe Unveiled",
      description: "Dive deep into the fascinating world of black holes and recent discoveries that are reshaping our understanding of space-time.",
      thumbnail: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=600&h=400&fit=crop&crop=center",
      duration: "8:32",
      views: "1.8M",
      publishedAt: "1 week ago",
      publisher: {
        name: "Cosmic Discoveries",
        avatar: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=40&h=40&fit=crop&crop=center"
      },
      videoUrl: "https://www.youtube.com/embed/watch?v=ujxjeMXStaw",
      category: "Science"
    },
    {
      id: 3,
      title: "International Space Station: Life in Zero Gravity",
      description: "Experience daily life aboard the ISS and learn about the groundbreaking research being conducted in microgravity.",
      thumbnail: "https://images.unsplash.com/photo-1517976487492-5750f3195933?w=600&h=400&fit=crop&crop=center",
      duration: "15:20",
      views: "3.1M",
      publishedAt: "5 days ago",
      publisher: {
        name: "NASA Official",
        avatar: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=40&h=40&fit=crop&crop=center"
      },
      videoUrl: "https://www.youtube.com/embed/watch?v=XkM_04Ch76E",
      category: "Technology"
    },
    {
      id: 4,
      title: "Exoplanets: Searching for Earth 2.0",
      description: "Join the hunt for potentially habitable exoplanets and the cutting-edge technology used to detect them.",
      thumbnail: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop&crop=center",
      duration: "10:15",
      views: "1.2M",
      publishedAt: "2 weeks ago",
      publisher: {
        name: "Kepler Mission",
        avatar: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=40&h=40&fit=crop&crop=center"
      },
      videoUrl: "https://www.youtube.com/embed/watch?v=9nCdo0iQ6KU",
      category: "Discovery"
    },
    {
      id: 5,
      title: "SpaceX Starship: Revolutionary Spacecraft Design",
      description: "Explore the innovative engineering behind SpaceX's Starship and its potential to revolutionize space travel.",
      thumbnail: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=600&h=400&fit=crop&crop=center",
      duration: "14:08",
      views: "4.5M",
      publishedAt: "1 day ago",
      publisher: {
        name: "SpaceX Updates",
        avatar: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=40&h=40&fit=crop&crop=center"
      },
      videoUrl: "https://www.youtube.com/embed/watch?v=qgFp1RydAqA",
      category: "Technology"
    },
  ];

  const handlePlayVideo = (videoId) => {
    setPlayingVideo(playingVideo === videoId ? null : videoId);
  };

  const formatViews = (views) => {
    return views;
  };

  return (
    <section className="video-section">
      <div className="video-container">
        <div className="video-header">
          <h2 className="video-title">Space Exploration Videos</h2>
          <button className="video-view-all">
            see all <span className="arrow">→</span>
          </button>
        </div>

        <div className="video-grid">
          {videoData.map((video) => (
            <article key={video.id} className={`video-card ${video.featured ? 'featured' : ''}`}>
              <div className="video-thumbnail">
                <img src={video.thumbnail} alt={video.title} />
                <div className="video-overlay">
                  <button 
                    className="play-button"
                    onClick={() => handlePlayVideo(video.id)}
                    aria-label={`Play ${video.title}`}
                  >
                    <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </button>
                  <div className="video-duration">{video.duration}</div>
                </div>
                <div className="category-badge">{video.category}</div>
              </div>

              <div className="video-content">
                <h3 className="video-card-title">{video.title}</h3>
                <p className="video-description">{video.description}</p>
                
                <div className="video-meta">
                  <div className="publisher-info">
                    <img src={video.publisher.avatar} alt={video.publisher.name} className="publisher-avatar" />
                    <div className="publisher-details">
                      <span className="publisher-name">{video.publisher.name}</span>
                      <div className="video-stats">
                        <span className="view-count">{formatViews(video.views)} views</span>
                        <span className="publish-date">{video.publishedAt}</span>
                      </div>
                    </div>
                  </div>
                  <button className="watch-btn">
                    Watch Now
                    <span className="btn-arrow">→</span>
                  </button>
                </div>
              </div>

              {playingVideo === video.id && (
                <div className="video-player-modal">
                  <div className="video-player-content">
                    <button 
                      className="close-player"
                      onClick={() => setPlayingVideo(null)}
                      aria-label="Close video player"
                    >
                      ×
                    </button>
                    <iframe
                      src={video.videoUrl}
                      title={video.title}
                      frameBorder="0"
                      allowFullScreen
                      className="video-iframe"
                    ></iframe>
                  </div>
                </div>
              )}
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Video;
