/* Latest Stories Section Styles */
.latest-stories {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.latest-stories::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.stories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.stories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
}

.stories-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stories-see-all {
  background: transparent;
  border: none;
  color: #b3b3b3;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 25px;
}

.stories-see-all:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.arrow {
  transition: transform 0.3s ease;
}

.stories-see-all:hover .arrow {
  transform: translateX(3px);
}

.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.story-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.story-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.story-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.story-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.story-card:hover .story-image img {
  transform: scale(1.05);
}

.story-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.story-content {
  padding: 1.5rem;
}

.story-title {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 1rem 0;
  color: white;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.story-description {
  color: #b3b3b3;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.story-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.story-author-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.story-publisher {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.publisher-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.publisher-avatar:hover {
  border-color: rgba(79, 70, 229, 0.5);
  transform: scale(1.1);
}

.story-author {
  color: #a8a8a8;
  font-size: 0.8rem;
  font-weight: 500;
}

.story-read-time {
  color: #888;
  font-size: 0.75rem;
}

.story-read-more {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.story-read-more:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .latest-stories {
    padding: 4rem 0;
  }

  .stories-container {
    padding: 0 1rem;
  }

  .stories-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  .stories-title {
    font-size: 2rem;
  }

  .stories-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .story-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .story-publisher {
    align-items: center;
  }

  .publisher-avatar {
    width: 20px;
    height: 20px;
  }

  .story-read-more {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stories-title {
    font-size: 1.75rem;
  }

  .story-content {
    padding: 1rem;
  }

  .story-image {
    height: 180px;
  }

  .publisher-avatar {
    width: 18px;
    height: 18px;
  }

  .story-author {
    font-size: 0.75rem;
  }
}
