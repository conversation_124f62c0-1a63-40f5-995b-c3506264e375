/* Trending News Section Styles */
.trending-news {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.trending-news::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.trending-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.trending-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
}

.trending-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.trending-view-all {
  background: transparent;
  border: none;
  color: #b3b3b3;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 25px;
}

.trending-view-all:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.arrow {
  transition: transform 0.3s ease;
}

.trending-view-all:hover .arrow {
  transform: translateX(3px);
}

.trending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.trending-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  cursor: pointer;
  position: relative;
}

.trending-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.trending-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.trending-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.trending-card:hover .trending-image img {
  transform: scale(1.05);
}

.trending-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: linear-gradient(135deg, #ec4899 0%, #7c3aed 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
}

.trending-icon {
  font-size: 0.8rem;
}

.trending-rank {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.trending-content {
  padding: 1.5rem;
}

.trending-category {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  margin-bottom: 1rem;
}

.trending-headline {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 1rem 0;
  color: white;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.trending-excerpt {
  color: #b3b3b3;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.trending-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.trending-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.trending-publisher {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.publisher-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.publisher-avatar:hover {
  border-color: rgba(79, 70, 229, 0.5);
  transform: scale(1.1);
}

.trending-source {
  color: #a8a8a8;
  font-size: 0.8rem;
  font-weight: 500;
}

.trending-date,
.trending-read-time {
  color: #888;
  font-size: 0.75rem;
}

.trending-read-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.trending-read-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.trending-read-btn:hover .btn-arrow {
  transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .trending-news {
    padding: 4rem 0;
  }

  .trending-container {
    padding: 0 1rem;
  }

  .trending-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  .trending-title {
    font-size: 2rem;
  }

  .trending-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .trending-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .trending-publisher {
    align-items: center;
  }

  .publisher-avatar {
    width: 20px;
    height: 20px;
  }

  .trending-read-btn {
    align-self: stretch;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .trending-title {
    font-size: 1.75rem;
  }

  .trending-content {
    padding: 1rem;
  }

  .trending-image {
    height: 180px;
  }

  .trending-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }

  .trending-rank {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 0.75rem;
  }

  .publisher-avatar {
    width: 18px;
    height: 18px;
  }

  .trending-source {
    font-size: 0.75rem;
  }
}
