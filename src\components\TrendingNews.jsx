import React from 'react';
import './TrendingNews.css';

const TrendingNews = () => {
  const trendingNews = [
    {
      id: 1,
      headline: "NASA's Artemis III Mission Selects Landing Sites Near Moon's South Pole",
      excerpt: "NASA has identified 13 candidate landing regions near the lunar south pole for the Artemis III mission, marking humanity's return to the Moon after more than 50 years.",
      source: "NASA News",
      publisherAvatar: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=40&h=40&fit=crop&crop=center",
      date: "2024-01-16",
      readTime: "4 mins read",
      trendingRank: 1,
      image: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=500&h=300&fit=crop&crop=center",
      category: "Moon Missions"
    },
    {
      id: 2,
      headline: "SpaceX Starship Successfully Completes In-Flight Fuel Transfer Test",
      excerpt: "SpaceX achieves a major milestone with successful propellant transfer between Starship vehicles in orbit, bringing Mars missions one step closer to reality.",
      source: "SpaceX Updates",
      publisherAvatar: "https://images.unsplash.com/photo-1517976487492-5750f3195933?w=40&h=40&fit=crop&crop=center",
      date: "2024-01-15",
      readTime: "6 mins read",
      trendingRank: 2,
      image: "https://images.unsplash.com/photo-1517976487492-5750f3195933?w=500&h=300&fit=crop&crop=center",
      category: "SpaceX"
    },
    {
      id: 3,
      headline: "James Webb Telescope Discovers Potentially Habitable Exoplanet",
      excerpt: "The James Webb Space Telescope has identified a rocky exoplanet with conditions that could support liquid water, located just 22 light-years from Earth.",
      source: "Space Telescope Science Institute",
      publisherAvatar: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=40&h=40&fit=crop&crop=center",
      date: "2024-01-14",
      readTime: "5 mins read",
      trendingRank: 3,
      image: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=500&h=300&fit=crop&crop=center",
      category: "Discoveries"
    },
    {
      id: 4,
      headline: "European Space Agency Announces New Mars Sample Return Timeline",
      excerpt: "ESA reveals updated plans for the Mars Sample Return mission, with launch window pushed to 2030 due to technical challenges and budget considerations.",
      source: "ESA Press Release",
      publisherAvatar: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=40&h=40&fit=crop&crop=center",
      date: "2024-01-13",
      readTime: "3 mins read",
      trendingRank: 4,
      image: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=500&h=300&fit=crop&crop=center",
      category: "Mars Missions"
    },
    {
      id: 5,
      headline: "Blue Origin Successfully Tests New Shepard's Upgraded Crew Capsule",
      excerpt: "Blue Origin completes critical safety tests for its upgraded New Shepard crew capsule, bringing commercial space tourism closer to regular operations.",
      source: "Blue Origin News",
      publisherAvatar: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=40&h=40&fit=crop&crop=center",
      date: "2024-01-12",
      readTime: "4 mins read",
      trendingRank: 5,
      image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=500&h=300&fit=crop&crop=center",
      category: "Space Tourism"
    },
    {
      id: 6,
      headline: "China's Chang'e 7 Mission Prepares for Lunar South Pole Exploration",
      excerpt: "China announces final preparations for the Chang'e 7 mission, which will explore the Moon's south pole and search for water ice deposits in permanently shadowed regions.",
      source: "CNSA Updates",
      publisherAvatar: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=40&h=40&fit=crop&crop=center",
      date: "2024-01-11",
      readTime: "5 mins read",
      trendingRank: 6,
      image: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=500&h=300&fit=crop&crop=center",
      category: "Lunar Exploration"
    }
  ];

  const getTrendingIcon = (rank) => {
    switch(rank) {
      case 1:
        return "🔥";
      case 2:
        return "⚡";
      case 3:
        return "🚀";
      case 4:
        return "⭐";
      case 5:
        return "💫";
      case 6:
        return "📈";
      default:
        return "📈";
    }
  };

  const getTrendingLabel = (rank) => {
    switch(rank) {
      case 1:
        return "Hot";
      case 2:
        return "Rising";
      case 3:
        return "Popular";
      case 4:
        return "Notable";
      case 5:
        return "Buzz";
      case 6:
        return "Trending";
      default:
        return "Trending";
    }
  };

  return (
    <section className="trending-news">
      <div className="trending-container">
        <div className="trending-header">
          <h2 className="trending-title">Trending News</h2>
          <button className="trending-view-all">
            View All <span className="arrow">→</span>
          </button>
        </div>

        <div className="trending-grid">
          {trendingNews.map((news) => (
            <article key={news.id} className="trending-card">
              <div className="trending-image">
                <img src={news.image} alt={news.headline} />
                <div className="trending-badge">
                  <span className="trending-icon">{getTrendingIcon(news.trendingRank)}</span>
                  <span className="trending-label">{getTrendingLabel(news.trendingRank)}</span>
                </div>
                <div className="trending-rank">#{news.trendingRank}</div>
              </div>

              <div className="trending-content">
                <div className="trending-category">{news.category}</div>
                <h3 className="trending-headline">{news.headline}</h3>
                <p className="trending-excerpt">{news.excerpt}</p>

                <div className="trending-meta">
                  <div className="trending-info">
                    <div className="trending-publisher">
                      <img src={news.publisherAvatar} alt={news.source} className="publisher-avatar" />
                      <span className="trending-source">{news.source}</span>
                    </div>
                    <span className="trending-date">{new Date(news.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                    <span className="trending-read-time">{news.readTime}</span>
                  </div>
                  <button className="trending-read-btn">
                    Read More
                    <span className="btn-arrow">→</span>
                  </button>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TrendingNews;
