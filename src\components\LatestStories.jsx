import React from 'react';
import './LatestStories.css';

const LatestStories = () => {
  const stories = [
    {
      id: 1,
      title: "Rocket Lab Aims to Return to Flight This Year After September Launch Failure",
      description: "Rocket Lab aims to return to flight this year after September launch failure. The company is still investigating the cause of the anomaly that occurred during the mission.",
      author: "Rocket Lab News",
      publisherAvatar: "https://images.unsplash.com/photo-1517976487492-5750f3195933?w=40&h=40&fit=crop&crop=center",
      readTime: "3 mins read",
      date: "2024-01-15",
      category: "Space Missions",
      image: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=250&fit=crop&crop=center"
    },
    {
      id: 2,
      title: "Space Force Planning $8 Billion Satellite Architecture for Nuclear Command and Control",
      description: "Space Force Planning $8 Billion Satellite Architecture for Nuclear Command and Control systems to enhance national security capabilities in space.",
      author: "Defense Space News",
      publisherAvatar: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=40&h=40&fit=crop&crop=center",
      readTime: "5 mins read",
      date: "2024-01-14",
      category: "Space Technology",
      image: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=400&h=250&fit=crop&crop=center"
    },
    {
      id: 3,
      title: "October's Orionids Meteor Shower are Linked to Halley's Comet: Data Analysis",
      description: "Want to know how to watch October's Orionids meteor shower and contemplate the wonders of Halley's comet? Here's everything you need to know.",
      author: "Astronomy Today",
      publisherAvatar: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=40&h=40&fit=crop&crop=center",
      readTime: "5 mins read",
      date: "2024-01-13",
      category: "Astronomy",
      image: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=250&fit=crop&crop=center"
    },
    {
      id: 4,
      title: "China Sends Three More Astronauts to Space Station to Replace Current Crew",
      description: "China sent three astronauts to its space station to replace the current crew. The Shenzhou-19 mission marks another milestone in China's space program.",
      author: "CNSA Updates",
      publisherAvatar: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=40&h=40&fit=crop&crop=center",
      readTime: "7 mins read",
      date: "2024-01-12",
      category: "Space Missions",
      image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=250&fit=crop&crop=center"
    },
    {
      id: 5,
      title: "International Space Station Prepares for Commercial Module Installation",
      description: "The International Space Station is preparing for the installation of new commercial modules that will expand research capabilities and commercial opportunities in low Earth orbit.",
      author: "ISS Research News",
      publisherAvatar: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=40&h=40&fit=crop&crop=center",
      readTime: "6 mins read",
      date: "2024-01-11",
      category: "Space Stations",
      image: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=250&fit=crop&crop=center"
    },
    {
      id: 6,
      title: "Virgin Galactic Announces Next Generation Spaceplane Development",
      description: "Virgin Galactic reveals plans for its next-generation spaceplane designed to carry more passengers and enable more frequent suborbital flights for space tourism.",
      author: "Virgin Galactic News",
      publisherAvatar: "https://images.unsplash.com/photo-1517976487492-5750f3195933?w=40&h=40&fit=crop&crop=center",
      readTime: "4 mins read",
      date: "2024-01-10",
      category: "Space Tourism",
      image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=250&fit=crop&crop=center"
    }
  ];

  return (
    <section className="latest-stories">
      <div className="stories-container">
        <div className="stories-header">
          <h2 className="stories-title">Latest Stories</h2>
          <button className="stories-see-all">
            see all <span className="arrow">→</span>
          </button>
        </div>

        <div className="stories-grid">
          {stories.map((story) => (
            <article key={story.id} className="story-card">
              <div className="story-image">
                <img src={story.image} alt={story.title} />
                <div className="story-category">{story.category}</div>
              </div>

              <div className="story-content">
                <h3 className="story-title">{story.title}</h3>
                <p className="story-description">{story.description}</p>

                <div className="story-meta">
                  <div className="story-author-info">
                    <div className="story-publisher">
                      <img src={story.publisherAvatar} alt={story.author} className="publisher-avatar" />
                      <span className="story-author">{story.author}</span>
                    </div>
                    <span className="story-read-time">{story.readTime}</span>
                  </div>
                  <button className="story-read-more">Read More</button>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LatestStories;
