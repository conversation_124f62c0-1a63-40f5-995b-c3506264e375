.planet-condition {
  padding: 2rem;
  background: #f8f9fa;
  min-height: 100vh;
}

.planet-container {
  max-width: 1200px;
  margin: 0 auto;
}

.planet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.planet-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.planet-view-all {
  background: transparent;
  border: none;
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.planet-view-all:hover {
  color: #333;
}

.arrow {
  transition: transform 0.3s ease;
}

.planet-view-all:hover .arrow {
  transform: translateX(4px);
}

.planet-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 1rem;
}

.planet-story {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.planet-story:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.planet-story.large-story {
  grid-row: span 2;
}

.story-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.large-story .story-image {
  height: 300px;
}

.story-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.planet-story:hover .story-image img {
  transform: scale(1.05);
}

.story-content {
  padding: 1.25rem;
}

.story-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  margin: 0 0 0.75rem 0;
}

.large-story .story-title {
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.story-description {
  font-size: 0.875rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.large-story .story-description {
  font-size: 0.9rem;
}

.read-more {
  color: #007bff;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .planet-condition {
    padding: 1rem;
  }

  .planet-title {
    font-size: 1.5rem;
  }

  .planet-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .planet-story.large-story {
    grid-row: span 1;
  }

  .story-image,
  .large-story .story-image {
    height: 200px;
  }

  .planet-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
