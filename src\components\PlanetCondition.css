.planet-condition {
  padding: 2rem;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.planet-condition::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #ffffff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.6), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.4), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.6), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  opacity: 0.3;
  pointer-events: none;
}

.planet-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.planet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.planet-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.planet-view-all {
  background: rgba(79, 70, 229, 0.2);
  border: 1px solid rgba(79, 70, 229, 0.3);
  color: #a5b4fc;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.planet-view-all:hover {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.arrow {
  transition: transform 0.3s ease;
}

.planet-view-all:hover .arrow {
  transform: translateX(4px);
}

.planet-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 1.5rem;
}

.planet-story {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
}

.planet-story::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.planet-story:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(79, 70, 229, 0.2);
}

.planet-story:hover::before {
  opacity: 1;
}

.planet-story.large-story {
  grid-row: span 2;
}

.story-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.large-story .story-image {
  height: 300px;
}

.story-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  filter: brightness(0.8) contrast(1.1);
}

.planet-story:hover .story-image img {
  transform: scale(1.05);
  filter: brightness(0.9) contrast(1.2);
}

.story-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
  pointer-events: none;
}

.story-content {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.story-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
  margin: 0 0 0.75rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.large-story .story-title {
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.story-description {
  font-size: 0.875rem;
  color: #d1d5db;
  line-height: 1.5;
  margin: 0;
  opacity: 0.9;
}

.large-story .story-description {
  font-size: 0.9rem;
}

.read-more {
  color: #a5b4fc;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  border-bottom: 1px solid transparent;
}

.read-more:hover {
  color: #ffffff;
  border-bottom-color: #a5b4fc;
  text-shadow: 0 0 8px rgba(165, 180, 252, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .planet-condition {
    padding: 1rem;
  }

  .planet-title {
    font-size: 1.5rem;
  }

  .planet-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .planet-story.large-story {
    grid-row: span 1;
  }

  .story-image,
  .large-story .story-image {
    height: 200px;
  }

  .planet-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .planet-view-all {
    align-self: flex-end;
  }

  .story-content {
    padding: 1.25rem;
  }
}

@media (max-width: 480px) {
  .planet-title {
    font-size: 1.25rem;
  }

  .story-content {
    padding: 1rem;
  }

  .story-title {
    font-size: 0.9rem;
  }

  .large-story .story-title {
    font-size: 1rem;
  }

  .story-description {
    font-size: 0.8rem;
  }
}
