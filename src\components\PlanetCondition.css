/* Planet Condition Section Styles */
.planet-condition {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.planet-condition::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.planet-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.planet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
}

.planet-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.planet-view-all {
  background: transparent;
  border: none;
  color: #b3b3b3;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 25px;
}

.planet-view-all:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.arrow {
  transition: transform 0.3s ease;
}

.planet-view-all:hover .arrow {
  transform: translateX(3px);
}

.planet-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.planet-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  cursor: pointer;
  position: relative;
}

.planet-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.planet-card.status-good:hover {
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 20px 40px rgba(16, 185, 129, 0.1);
}

.planet-card.status-warning:hover {
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 20px 40px rgba(245, 158, 11, 0.1);
}

.planet-card.status-critical:hover {
  border-color: rgba(239, 68, 68, 0.3);
  box-shadow: 0 20px 40px rgba(239, 68, 68, 0.1);
}

.planet-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.metric-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.metric-icon {
  font-size: 1.5rem;
}

.metric-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.status-good {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-badge.status-warning {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-badge.status-critical {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.metric-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.current-value {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.trend-icon {
  font-size: 1rem;
}

.trend-value {
  font-size: 0.8rem;
  font-weight: 500;
  color: #b3b3b3;
}

.progress-container {
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.threshold-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.threshold-label {
  font-size: 0.8rem;
  color: #888;
}

.percentage-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #b3b3b3;
}

.metric-description {
  color: #b3b3b3;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 1.5rem 0;
}

.planet-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.source-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.source-publisher {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.source-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.source-name {
  color: #a8a8a8;
  font-size: 0.8rem;
  font-weight: 500;
}

.last-updated {
  color: #888;
  font-size: 0.75rem;
}

.details-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.details-btn:hover .btn-arrow {
  transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .planet-condition {
    padding: 4rem 0;
  }

  .planet-container {
    padding: 0 1rem;
  }

  .planet-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  .planet-title {
    font-size: 2rem;
  }

  .planet-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .planet-card {
    padding: 1.25rem;
  }

  .planet-card-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .metric-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .current-value {
    font-size: 1.75rem;
  }

  .planet-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .details-btn {
    align-self: stretch;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .planet-title {
    font-size: 1.75rem;
  }

  .planet-card {
    padding: 1rem;
  }

  .current-value {
    font-size: 1.5rem;
  }

  .metric-name {
    font-size: 1rem;
  }
}
