import React from 'react';
import './PlanetCondition.css';

const PlanetCondition = () => {
  const planetStories = [
    {
      id: 1,
      title: "New Research Ties Industrial Pollution in Joppa to Higher Rates of Asthma, Respiratory Issues",
      description: "Joppa residents breathe more polluted air and report higher cases of asthma and other respiratory issues than local and state averages. These are the top-line takeaways of a new study by researchers...",
      image: "https://images.unsplash.com/photo-1611273426858-450d8e3c9fce?w=500&h=300&fit=crop&crop=center",
      readMore: "Read more",
      isLarge: true
    },
    {
      id: 2,
      title: "Wildlife Worldwide Contaminated by Flame Retardants: New Map",
      description: "Wild animals across every continent are contaminated with flame retardant chemicals, according to a new...",
      image: "https://images.unsplash.com/photo-1549366021-9f761d040a94?w=400&h=250&fit=crop&crop=center",
      readMore: "Read more"
    },
    {
      id: 3,
      title: "Switzerland Sets Its Sights on Combatting Greenwashing in Fina...",
      description: "Switzerland's Federal Department of Finance (FDF) has declared its intention to put forward regulation...",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop&crop=center",
      readMore: "Read more"
    },
    {
      id: 4,
      title: "For years, Japan Tried to Keep Their Existence a Secret, But the...",
      description: "As a young boy in school, Masaki Sashima would be dragged out of his classroom and beaten by...",
      image: "https://images.unsplash.com/photo-1490806843957-31f4c9a91c65?w=400&h=250&fit=crop&crop=center",
      readMore: "Read more"
    },
    {
      id: 5,
      title: "Prospect of $4 Million a Year for Trash Collection Leaves New Ken...",
      description: "The prospect of paying nearly $4 million a year for curbside trash collection — more than four times what...",
      image: "https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?w=400&h=250&fit=crop&crop=center",
      readMore: "Read more"
    }
  ];

  return (
    <section className="planet-condition">
      <div className="planet-container">
        <div className="planet-header">
          <h2 className="planet-title">Current Condition of Our Planet</h2>
          <button className="planet-view-all">
            see all <span className="arrow">→</span>
          </button>
        </div>

        <div className="planet-grid">
          {planetStories.map((story) => (
            <article key={story.id} className={`planet-story ${story.isLarge ? 'large-story' : ''}`}>
              <div className="story-image">
                <img src={story.image} alt={story.title} />
              </div>
              <div className="story-content">
                <h3 className="story-title">{story.title}</h3>
                <p className="story-description">
                  {story.description} <span className="read-more">{story.readMore}</span>
                </p>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PlanetCondition;
