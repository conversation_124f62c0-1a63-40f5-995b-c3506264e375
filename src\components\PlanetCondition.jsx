import React from 'react';
import './PlanetCondition.css';

const PlanetCondition = () => {
  const planetData = [
    {
      id: 1,
      metric: "Global Temperature",
      currentValue: "15.2°C",
      threshold: "14.0°C",
      percentage: 86,
      trend: "up",
      trendValue: "+1.2°C",
      status: "warning",
      lastUpdated: "2024-01-16",
      source: "NASA GISS",
      sourceAvatar: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=40&h=40&fit=crop&crop=center",
      description: "Global mean surface temperature anomaly",
      icon: "🌡️"
    },
    {
      id: 2,
      metric: "Atmospheric CO₂",
      currentValue: "421.3 ppm",
      threshold: "350 ppm",
      percentage: 120,
      trend: "up",
      trendValue: "+2.4 ppm/year",
      status: "critical",
      lastUpdated: "2024-01-15",
      source: "NOAA ESRL",
      sourceAvatar: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=40&h=40&fit=crop&crop=center",
      description: "Atmospheric carbon dioxide concentration",
      icon: "🏭"
    },
    {
      id: 3,
      metric: "Arctic Sea Ice",
      currentValue: "13.8M km²",
      threshold: "15.6M km²",
      percentage: 88,
      trend: "down",
      trendValue: "-13% per decade",
      status: "warning",
      lastUpdated: "2024-01-14",
      source: "NSIDC",
      sourceAvatar: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=40&h=40&fit=crop&crop=center",
      description: "Arctic sea ice extent minimum",
      icon: "🧊"
    },
    {
      id: 4,
      metric: "Ocean pH Level",
      currentValue: "8.05",
      threshold: "8.25",
      percentage: 76,
      trend: "down",
      trendValue: "-0.1 units",
      status: "warning",
      lastUpdated: "2024-01-13",
      source: "NOAA PMEL",
      sourceAvatar: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=40&h=40&fit=crop&crop=center",
      description: "Global ocean surface pH levels",
      icon: "🌊"
    },
    {
      id: 5,
      metric: "Ozone Layer",
      currentValue: "285 DU",
      threshold: "300 DU",
      percentage: 95,
      trend: "stable",
      trendValue: "Recovering",
      status: "good",
      lastUpdated: "2024-01-12",
      source: "ESA Copernicus",
      sourceAvatar: "https://images.unsplash.com/photo-1517976487492-5750f3195933?w=40&h=40&fit=crop&crop=center",
      description: "Stratospheric ozone concentration",
      icon: "🛡️"
    },
    {
      id: 6,
      metric: "Forest Coverage",
      currentValue: "31.2%",
      threshold: "35.0%",
      percentage: 89,
      trend: "down",
      trendValue: "-0.2% annually",
      status: "warning",
      lastUpdated: "2024-01-11",
      source: "FAO Global FRA",
      sourceAvatar: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=40&h=40&fit=crop&crop=center",
      description: "Global forest area percentage",
      icon: "🌲"
    }
  ];

  const getStatusColor = (status) => {
    switch(status) {
      case 'good':
        return '#10b981'; // Green
      case 'warning':
        return '#f59e0b'; // Yellow
      case 'critical':
        return '#ef4444'; // Red
      default:
        return '#6b7280'; // Gray
    }
  };

  const getTrendIcon = (trend) => {
    switch(trend) {
      case 'up':
        return '↗️';
      case 'down':
        return '↘️';
      case 'stable':
        return '➡️';
      default:
        return '➡️';
    }
  };

  const getStatusLabel = (status) => {
    switch(status) {
      case 'good':
        return 'Good';
      case 'warning':
        return 'Warning';
      case 'critical':
        return 'Critical';
      default:
        return 'Unknown';
    }
  };

  return (
    <section className="planet-condition">
      <div className="planet-container">
        <div className="planet-header">
          <h2 className="planet-title">Current Condition of Our Planet</h2>
          <button className="planet-view-all">
            View All <span className="arrow">→</span>
          </button>
        </div>
        
        <div className="planet-grid">
          {planetData.map((data) => (
            <article key={data.id} className={`planet-card status-${data.status}`}>
              <div className="planet-card-header">
                <div className="metric-info">
                  <span className="metric-icon">{data.icon}</span>
                  <h3 className="metric-name">{data.metric}</h3>
                </div>
                <div className={`status-badge status-${data.status}`}>
                  {getStatusLabel(data.status)}
                </div>
              </div>
              
              <div className="metric-value">
                <span className="current-value">{data.currentValue}</span>
                <div className="trend-indicator">
                  <span className="trend-icon">{getTrendIcon(data.trend)}</span>
                  <span className="trend-value">{data.trendValue}</span>
                </div>
              </div>
              
              <div className="progress-container">
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ 
                      width: `${Math.min(data.percentage, 100)}%`,
                      backgroundColor: getStatusColor(data.status)
                    }}
                  ></div>
                </div>
                <div className="threshold-info">
                  <span className="threshold-label">Threshold: {data.threshold}</span>
                  <span className="percentage-value">{data.percentage}%</span>
                </div>
              </div>
              
              <p className="metric-description">{data.description}</p>
              
              <div className="planet-meta">
                <div className="source-info">
                  <div className="source-publisher">
                    <img src={data.sourceAvatar} alt={data.source} className="source-avatar" />
                    <span className="source-name">{data.source}</span>
                  </div>
                  <span className="last-updated">
                    Updated: {new Date(data.lastUpdated).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </span>
                </div>
                <button className="details-btn">
                  View Details
                  <span className="btn-arrow">→</span>
                </button>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PlanetCondition;
