/* Video Section Styles */
.video-section {
  padding: 2rem;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.video-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #ffffff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.6), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.4), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.6), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  opacity: 0.3;
  pointer-events: none;
}

.video-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.video-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.video-view-all {
  background: rgba(79, 70, 229, 0.2);
  border: 1px solid rgba(79, 70, 229, 0.3);
  color: #a5b4fc;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.video-view-all:hover {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.arrow {
  transition: transform 0.3s ease;
}

.video-view-all:hover .arrow {
  transform: translateX(4px);
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.video-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
}

.video-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(124, 58, 237, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.video-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(79, 70, 229, 0.2);
}

.video-card:hover::before {
  opacity: 1;
}

.video-card.featured {
  grid-column: span 2;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.video-card.featured .video-thumbnail {
  height: 300px;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  filter: brightness(0.8) contrast(1.1);
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
  filter: brightness(0.9) contrast(1.2);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.4) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-card:hover .video-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  background: rgba(79, 70, 229, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.play-button:hover {
  background: rgba(79, 70, 229, 1);
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.6);
}

.play-icon {
  width: 24px;
  height: 24px;
  color: #ffffff;
  margin-left: 2px;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.category-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(79, 70, 229, 0.9);
  color: #ffffff;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.video-content {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.video-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
  margin: 0 0 0.75rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.video-card.featured .video-card-title {
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.video-description {
  font-size: 0.875rem;
  color: #d1d5db;
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
  opacity: 0.9;
}

.video-card.featured .video-description {
  font-size: 0.9rem;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.publisher-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.publisher-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.publisher-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.publisher-name {
  font-size: 0.85rem;
  font-weight: 500;
  color: #ffffff;
}

.video-stats {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #9ca3af;
}

.view-count::after {
  content: '•';
  margin-left: 0.5rem;
  color: #6b7280;
}

.watch-btn {
  background: rgba(79, 70, 229, 0.2);
  border: 1px solid rgba(79, 70, 229, 0.3);
  color: #a5b4fc;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.watch-btn:hover {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  color: #ffffff;
  transform: translateY(-1px);
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.watch-btn:hover .btn-arrow {
  transform: translateX(2px);
}

/* Video Player Modal */
.video-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.video-player-content {
  position: relative;
  width: 100%;
  max-width: 900px;
  aspect-ratio: 16/9;
}

.close-player {
  position: absolute;
  top: -40px;
  right: 0;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #ffffff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-player:hover {
  background: rgba(255, 255, 255, 0.2);
}

.video-iframe {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-section {
    padding: 1rem;
  }
  
  .video-title {
    font-size: 1.5rem;
  }
  
  .video-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .video-card.featured {
    grid-column: span 1;
  }
  
  .video-thumbnail,
  .video-card.featured .video-thumbnail {
    height: 200px;
  }
  
  .video-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .video-view-all {
    align-self: flex-end;
  }
  
  .video-content {
    padding: 1.25rem;
  }
  
  .video-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .watch-btn {
    align-self: stretch;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .video-title {
    font-size: 1.25rem;
  }
  
  .video-content {
    padding: 1rem;
  }
  
  .video-card-title {
    font-size: 1rem;
  }
  
  .video-card.featured .video-card-title {
    font-size: 1.1rem;
  }
  
  .video-description {
    font-size: 0.8rem;
  }
  
  .video-player-modal {
    padding: 1rem;
  }
}
